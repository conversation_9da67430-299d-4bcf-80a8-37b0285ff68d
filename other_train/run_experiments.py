"""
批量实验执行脚本
运行所有对比模型的训练和测试
"""
import os
import sys
import argparse
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from other_train.utils.experiment_runner import ExperimentRunner, run_comparison_experiments
from other_train.trainers import get_train_function


def run_single_model_experiment(model_name: str, dataset: str):
    """运行单个模型实验"""
    print(f"\n🚀 开始单个实验: {model_name.upper()} on {dataset.upper()}")
    
    runner = ExperimentRunner()
    result = runner.run_single_experiment(model_name, dataset)
    
    if result['status'] == 'success':
        print(f"\n✅ 实验成功完成!")
        if result['test_results']:
            print(f"测试结果:")
            for metric, value in result['test_results'].items():
                print(f"  {metric.upper()}: {value:.4f}")
    else:
        print(f"\n❌ 实验失败: {result['error_message']}")
    
    return result


def run_model_on_all_datasets(model_name: str):
    """在所有数据集上运行指定模型"""
    datasets = ['aptos', 'odir']
    
    print(f"\n🚀 开始运行 {model_name.upper()} 在所有数据集上")
    
    runner = ExperimentRunner()
    results = {}
    
    for dataset in datasets:
        print(f"\n--- 运行 {model_name.upper()} on {dataset.upper()} ---")
        result = runner.run_single_experiment(model_name, dataset)
        results[f"{model_name}_{dataset}"] = result
    
    # 保存结果
    runner._save_final_results(results)
    runner._generate_analysis(results)
    
    return results


def run_dataset_comparison(dataset: str):
    """在指定数据集上比较所有模型"""
    models = ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']
    
    print(f"\n🚀 开始在 {dataset.upper()} 数据集上比较所有模型")
    
    runner = ExperimentRunner()
    results = {}
    
    for model_name in models:
        print(f"\n--- 运行 {model_name.upper()} on {dataset.upper()} ---")
        result = runner.run_single_experiment(model_name, dataset)
        results[f"{model_name}_{dataset}"] = result
    
    # 保存结果
    runner._save_final_results(results)
    runner._generate_analysis(results)
    
    # 显示排名
    primary_metric = 'qwk' if dataset == 'aptos' else 'auc'
    print(f"\n🏆 {dataset.upper()}数据集上的模型性能排名:")
    
    successful_results = []
    for key, result in results.items():
        if result['status'] == 'success' and result['test_results']:
            model_name = result['model_name']
            metric_value = result['test_results'].get(primary_metric, 0)
            successful_results.append((model_name, metric_value))
    
    # 排序并显示
    successful_results.sort(key=lambda x: x[1], reverse=True)
    for rank, (model, score) in enumerate(successful_results, 1):
        print(f"{rank}. {model.upper()}: {score:.4f}")
    
    return results


def validate_experiment_fairness():
    """验证实验公平性"""
    print("\n🔍 验证实验公平性...")
    
    from other_train.config import get_config
    
    models = ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']
    datasets = ['aptos', 'odir']
    
    print("\n📋 配置一致性检查:")
    
    for dataset in datasets:
        print(f"\n{dataset.upper()} 数据集配置:")
        base_config = None
        
        for model in models:
            config = get_config(dataset, model)
            
            # 检查关键参数
            key_params = {
                'batch_size': config.batch_size,
                'learning_rate': config.learning_rate,
                'weight_decay': config.weight_decay,
                'optimizer': config.optimizer_name,
                'scheduler': config.scheduler_name,
                'epochs': config.epochs,
                'random_seed': config.random_seed,
                'image_size': config.image_size,
                'normalize_mean': config.normalize_mean,
                'normalize_std': config.normalize_std
            }
            
            if base_config is None:
                base_config = key_params
                print(f"  基准配置 ({model}):")
                for param, value in key_params.items():
                    print(f"    {param}: {value}")
            else:
                # 检查是否一致
                inconsistent = []
                for param, value in key_params.items():
                    if value != base_config[param]:
                        inconsistent.append(f"{param}: {value} vs {base_config[param]}")
                
                if inconsistent:
                    print(f"  ❌ {model} 配置不一致:")
                    for item in inconsistent:
                        print(f"    {item}")
                else:
                    print(f"  ✅ {model} 配置一致")
    
    print("\n✅ 实验公平性验证完成!")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='模型对比实验执行脚本')
    parser.add_argument('--mode', type=str, 
                       choices=['single', 'model_all', 'dataset_all', 'full', 'validate'],
                       default='full',
                       help='实验模式')
    parser.add_argument('--model', type=str, 
                       choices=['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets'],
                       help='指定模型（single和model_all模式需要）')
    parser.add_argument('--dataset', type=str, 
                       choices=['aptos', 'odir'],
                       help='指定数据集（single和dataset_all模式需要）')
    
    args = parser.parse_args()
    
    print("🔬 模型对比实验系统")
    print("=" * 60)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"执行模式: {args.mode}")
    
    if args.mode == 'single':
        # 运行单个实验
        if not args.model or not args.dataset:
            print("❌ single模式需要指定--model和--dataset参数")
            return
        
        run_single_model_experiment(args.model, args.dataset)
    
    elif args.mode == 'model_all':
        # 在所有数据集上运行指定模型
        if not args.model:
            print("❌ model_all模式需要指定--model参数")
            return
        
        run_model_on_all_datasets(args.model)
    
    elif args.mode == 'dataset_all':
        # 在指定数据集上比较所有模型
        if not args.dataset:
            print("❌ dataset_all模式需要指定--dataset参数")
            return
        
        run_dataset_comparison(args.dataset)
    
    elif args.mode == 'full':
        # 运行完整对比实验
        print("🚀 开始完整对比实验...")
        run_comparison_experiments()
    
    elif args.mode == 'validate':
        # 验证实验公平性
        validate_experiment_fairness()
    
    print(f"\n🎉 实验执行完成! ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")


if __name__ == "__main__":
    main()
