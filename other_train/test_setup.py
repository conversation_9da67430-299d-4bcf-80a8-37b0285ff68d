"""
测试脚本 - 验证所有组件是否正确设置
"""
import os
import sys
import torch

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试配置模块
        from other_train.config import get_config, create_base_config
        print("  ✅ 配置模块导入成功")
        
        # 测试模型模块
        from other_train.models import (
            create_densenet, create_efficientnet, create_vit, 
            create_se_resnet, create_msnets
        )
        print("  ✅ 模型模块导入成功")
        
        # 测试训练器模块
        from other_train.trainers import (
            DenseNetTrainer, EfficientNetTrainer, ViTTrainer,
            SEResNetTrainer, MSNetsTrainer
        )
        print("  ✅ 训练器模块导入成功")
        
        # 测试工具模块
        from other_train.utils import (
            calculate_metrics, MetricsTracker, ExperimentRunner
        )
        print("  ✅ 工具模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False


def test_configs():
    """测试配置系统"""
    print("\n🔍 测试配置系统...")
    
    try:
        from other_train.config import get_config
        
        models = ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']
        datasets = ['aptos', 'odir']
        
        for dataset in datasets:
            for model in models:
                config = get_config(dataset, model)
                assert config.dataset_name.lower() == dataset
                assert hasattr(config, 'num_classes')
                assert hasattr(config, 'epochs')
                assert hasattr(config, 'batch_size')
                assert hasattr(config, 'learning_rate')
        
        print("  ✅ 所有配置创建成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False


def test_models():
    """测试模型创建"""
    print("\n🔍 测试模型创建...")
    
    try:
        from other_train.models import (
            create_densenet, create_efficientnet, create_vit,
            create_se_resnet, create_msnets
        )
        
        # 测试每个模型的创建
        models_to_test = [
            ('DenseNet', create_densenet),
            ('EfficientNet', create_efficientnet),
            ('ViT', create_vit),
            ('SE-ResNet', create_se_resnet),
            ('MSNets', create_msnets)
        ]
        
        for model_name, create_func in models_to_test:
            try:
                # 创建小模型进行测试
                model = create_func(num_classes=5, pretrained=False)
                
                # 测试前向传播
                x = torch.randn(1, 3, 224, 224)
                with torch.no_grad():
                    output = model(x)
                
                assert output.shape == (1, 5), f"{model_name} 输出形状错误: {output.shape}"
                print(f"  ✅ {model_name} 创建和前向传播成功")
                
            except Exception as e:
                print(f"  ❌ {model_name} 测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模型测试失败: {e}")
        return False


def test_metrics():
    """测试评估指标"""
    print("\n🔍 测试评估指标...")
    
    try:
        from other_train.utils import calculate_metrics, MetricsTracker
        import numpy as np
        
        # 测试APTOS指标
        y_true = np.array([0, 1, 2, 3, 4, 0, 1, 2])
        y_pred = np.array([0, 1, 1, 3, 4, 1, 1, 2])
        y_prob = np.random.rand(8, 5)
        
        aptos_metrics = calculate_metrics(y_true, y_pred, y_prob, 5, "aptos")
        assert 'qwk' in aptos_metrics
        assert 'accuracy' in aptos_metrics
        print("  ✅ APTOS指标计算成功")
        
        # 测试ODIR指标
        y_true_odir = np.array([0, 1, 0, 1, 1, 0])
        y_pred_odir = np.array([0, 1, 1, 1, 0, 0])
        y_prob_odir = np.random.rand(6, 2)
        
        odir_metrics = calculate_metrics(y_true_odir, y_pred_odir, y_prob_odir, 2, "odir")
        assert 'auc' in odir_metrics
        assert 'accuracy' in odir_metrics
        print("  ✅ ODIR指标计算成功")
        
        # 测试MetricsTracker
        tracker = MetricsTracker("aptos", 5)
        tracker.update(torch.tensor(y_true), torch.tensor(y_pred), torch.tensor(y_prob))
        metrics = tracker.compute()
        assert 'qwk' in metrics
        print("  ✅ MetricsTracker测试成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 指标测试失败: {e}")
        return False


def test_experiment_fairness():
    """测试实验公平性"""
    print("\n🔍 测试实验公平性...")
    
    try:
        from other_train.config import get_config
        
        models = ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']
        datasets = ['aptos', 'odir']
        
        for dataset in datasets:
            base_config = None
            
            for model in models:
                config = get_config(dataset, model)
                
                # 检查关键参数
                key_params = {
                    'batch_size': config.batch_size,
                    'learning_rate': config.learning_rate,
                    'weight_decay': config.weight_decay,
                    'optimizer': config.optimizer_name,
                    'scheduler': config.scheduler_name,
                    'random_seed': config.random_seed,
                    'image_size': config.image_size
                }
                
                if base_config is None:
                    base_config = key_params
                else:
                    # 检查一致性
                    for param, value in key_params.items():
                        assert value == base_config[param], \
                            f"{dataset}-{model}: {param} 不一致 ({value} vs {base_config[param]})"
            
            print(f"  ✅ {dataset.upper()} 数据集配置一致性验证通过")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 公平性测试失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    required_dirs = [
        'other_train/config',
        'other_train/models',
        'other_train/trainers',
        'other_train/utils'
    ]
    
    required_files = [
        'other_train/config/__init__.py',
        'other_train/config/base_config.py',
        'other_train/config/aptos_config.py',
        'other_train/config/odir_config.py',
        'other_train/models/__init__.py',
        'other_train/models/densenet.py',
        'other_train/models/efficientnet.py',
        'other_train/models/vit.py',
        'other_train/models/se_resnet.py',
        'other_train/models/msnets.py',
        'other_train/trainers/__init__.py',
        'other_train/trainers/base_trainer.py',
        'other_train/utils/__init__.py',
        'other_train/utils/metrics.py',
        'other_train/utils/experiment_runner.py',
        'other_train/run_experiments.py'
    ]
    
    try:
        # 检查目录
        for dir_path in required_dirs:
            assert os.path.isdir(dir_path), f"目录不存在: {dir_path}"
        
        # 检查文件
        for file_path in required_files:
            assert os.path.isfile(file_path), f"文件不存在: {file_path}"
        
        print("  ✅ 目录结构完整")
        return True
        
    except Exception as e:
        print(f"  ❌ 目录结构测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 开始系统测试")
    print("=" * 50)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("模块导入", test_imports),
        ("配置系统", test_configs),
        ("模型创建", test_models),
        ("评估指标", test_metrics),
        ("实验公平性", test_experiment_fairness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 系统准备就绪!")
        print("\n📋 使用说明:")
        print("1. 运行单个实验: python other_train/run_experiments.py --mode single --model densenet --dataset aptos")
        print("2. 运行完整对比: python other_train/run_experiments.py --mode full")
        print("3. 验证公平性: python other_train/run_experiments.py --mode validate")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    return passed == total


if __name__ == "__main__":
    main()
